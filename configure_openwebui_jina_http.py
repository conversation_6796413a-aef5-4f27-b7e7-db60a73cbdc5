#!/usr/bin/env python3
"""
Configure Open WebUI to use Jina Crawler HTTP endpoint
This script configures Open WebUI to use HTTP-based tools instead of MCP
"""

import json
import requests
import time
import sys
from typing import Dict, Any

def check_jina_crawler_health():
    """Check if Jina Crawler HTTP server is running"""
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Jina Crawler HTTP Server is healthy")
            print(f"   Status: {data.get('status')}")
            print(f"   Components: {data.get('components')}")
            return True
        else:
            print(f"❌ Jina Crawler HTTP Server returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Jina Crawler HTTP Server: {e}")
        return False

def check_openwebui_health():
    """Check if Open WebUI is running"""
    try:
        # Try common Open WebUI ports
        ports = [3000, 8080, 80]
        for port in ports:
            try:
                response = requests.get(f"http://localhost:{port}/health", timeout=5)
                if response.status_code == 200:
                    print(f"✅ Open WebUI is running on port {port}")
                    return port
            except:
                continue
        
        print("❌ Open WebUI is not accessible on common ports (3000, 8080, 80)")
        return None
    except Exception as e:
        print(f"❌ Error checking Open WebUI: {e}")
        return None

def configure_openwebui_tools(openwebui_port: int):
    """Configure Open WebUI to use Jina Crawler HTTP tools"""
    try:
        # Load the tool configuration
        with open('openwebui_jina_crawler_config.json', 'r') as f:
            tool_config = json.load(f)
        
        # Open WebUI API endpoint for tools configuration
        api_url = f"http://localhost:{openwebui_port}/api/v1/tools"
        
        # Headers for API request
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        print(f"🔧 Configuring Open WebUI tools at {api_url}")
        
        # Register the Jina Crawler tool
        tool_data = {
            "name": tool_config["name"],
            "description": tool_config["description"],
            "url": tool_config["url"],
            "enabled": tool_config["enabled"]
        }
        
        response = requests.post(api_url, json=tool_data, headers=headers, timeout=10)
        
        if response.status_code in [200, 201]:
            print("✅ Jina Crawler tool registered successfully in Open WebUI")
            return True
        else:
            print(f"❌ Failed to register tool: {response.status_code} - {response.text}")
            return False
            
    except FileNotFoundError:
        print("❌ Configuration file 'openwebui_jina_crawler_config.json' not found")
        return False
    except Exception as e:
        print(f"❌ Error configuring Open WebUI tools: {e}")
        return False

def update_mcpo_config():
    """Update MCPO config to use HTTP endpoint instead of MCP"""
    try:
        config_path = "mem0-owui/config/mcpo_config.json"
        
        # Read current config
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Remove jina_crawler from MCP servers and add HTTP endpoint info
        if "mcpServers" in config and "jina_crawler" in config["mcpServers"]:
            del config["mcpServers"]["jina_crawler"]
            print("✅ Removed jina_crawler from MCP servers")
        
        # Add HTTP endpoint configuration
        if "httpTools" not in config:
            config["httpTools"] = {}
        
        config["httpTools"]["jina_crawler"] = {
            "name": "jina_crawler",
            "description": "Smart web crawler with AI-powered content processing",
            "base_url": "http://localhost:8001",
            "openapi_url": "http://localhost:8001/jina_crawler/openapi.json",
            "enabled": True
        }
        
        # Write updated config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Updated MCPO config to use HTTP endpoint")
        return True
        
    except FileNotFoundError:
        print("❌ MCPO config file not found")
        return False
    except Exception as e:
        print(f"❌ Error updating MCPO config: {e}")
        return False

def create_openwebui_function():
    """Create Open WebUI function for Jina Crawler"""
    function_code = '''
def jina_crawler_tool(url: str, method: str = "tls_bypass", max_content_length: int = 10000) -> str:
    """
    Smart web crawler with AI-powered content processing using Jina and Gemini.
    
    Args:
        url: URL to crawl
        method: Crawling method (tls_bypass or standard)
        max_content_length: Maximum content length
    
    Returns:
        Processed content from the URL
    """
    import requests
    import json
    
    try:
        payload = {
            "url": url,
            "method": method,
            "process_content": True,
            "max_content_length": max_content_length
        }
        
        response = requests.post(
            "http://localhost:8001/jina_crawler/crawl",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                return f"Title: {data.get('title', 'N/A')}\\n\\nContent:\\n{data.get('content', '')}"
            else:
                return f"Error: {data.get('error', 'Unknown error')}"
        else:
            return f"HTTP Error: {response.status_code}"
            
    except Exception as e:
        return f"Error calling Jina Crawler: {str(e)}"
'''
    
    # Save function to file
    with open('openwebui_jina_crawler_function.py', 'w') as f:
        f.write(function_code)
    
    print("✅ Created Open WebUI function file: openwebui_jina_crawler_function.py")
    print("📋 You can copy this function into Open WebUI Functions section")

def main():
    """Main configuration process"""
    print("🚀 Configuring Open WebUI to use Jina Crawler HTTP endpoint")
    print("=" * 60)
    
    # Step 1: Check Jina Crawler HTTP server
    print("1. Checking Jina Crawler HTTP Server...")
    if not check_jina_crawler_health():
        print("❌ Please start Jina Crawler HTTP Server first:")
        print("   python3 jina_crawler_http_server.py")
        sys.exit(1)
    
    # Step 2: Check Open WebUI
    print("\\n2. Checking Open WebUI...")
    openwebui_port = check_openwebui_health()
    if not openwebui_port:
        print("❌ Please start Open WebUI first")
        sys.exit(1)
    
    # Step 3: Update MCPO config
    print("\\n3. Updating MCPO configuration...")
    update_mcpo_config()
    
    # Step 4: Create Open WebUI function
    print("\\n4. Creating Open WebUI function...")
    create_openwebui_function()
    
    print("\\n" + "=" * 60)
    print("✅ Configuration completed successfully!")
    print("\\n📋 Next steps:")
    print("1. Restart Open WebUI to load new configuration")
    print("2. Go to Open WebUI Admin Panel > Functions")
    print("3. Create a new function and paste the code from 'openwebui_jina_crawler_function.py'")
    print("4. Enable the function and test it")
    print("\\n🌐 Jina Crawler HTTP Server: http://localhost:8001")
    print("📖 API Documentation: http://localhost:8001/docs")

if __name__ == "__main__":
    main()
