{"name": "jina_crawler", "description": "Smart web crawler with AI-powered content processing using Jina and Gemini", "url": "http://localhost:8001/jina_crawler/openapi.json", "base_url": "http://localhost:8001", "enabled": true, "tools": [{"name": "crawl_url", "description": "Crawl and process content from a single URL", "endpoint": "/jina_crawler/crawl", "method": "POST", "parameters": {"url": {"type": "string", "description": "URL to crawl", "required": true}, "method": {"type": "string", "description": "Crawling method", "default": "tls_bypass", "enum": ["tls_bypass", "standard"]}, "process_content": {"type": "boolean", "description": "Enable AI processing", "default": true}, "max_content_length": {"type": "integer", "description": "Maximum content length", "default": 10000}}}, {"name": "crawl_batch", "description": "Crawl and process content from multiple URLs in parallel", "endpoint": "/jina_crawler/crawl_batch", "method": "POST", "parameters": {"urls": {"type": "array", "items": {"type": "string"}, "description": "List of URLs to crawl", "required": true}, "method": {"type": "string", "description": "Crawling method", "default": "tls_bypass"}, "process_content": {"type": "boolean", "description": "Enable AI processing", "default": true}, "max_content_length": {"type": "integer", "description": "Maximum content length per URL", "default": 10000}}}, {"name": "ai_search", "description": "Search the web with AI-powered query refinement and content synthesis", "endpoint": "/jina_crawler/ai_search", "method": "POST", "parameters": {"query": {"type": "string", "description": "Search query", "required": true}, "max_sources": {"type": "integer", "description": "Maximum number of sources", "default": 10}, "enable_query_refinement": {"type": "boolean", "description": "Enable query refinement", "default": true}}}, {"name": "bypass_paywall", "description": "Extract content from paywall-protected URLs", "endpoint": "/jina_crawler/bypass_paywall", "method": "POST", "parameters": {"url": {"type": "string", "description": "Paywall-protected URL", "required": true}, "max_content_length": {"type": "integer", "description": "Maximum content length", "default": 50000}}}]}