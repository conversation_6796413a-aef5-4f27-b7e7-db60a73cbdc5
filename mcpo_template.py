#!/usr/bin/env python3
"""
MCPO Template for Open WebUI Integration
Template for creating MCP OpenAPI Proxy servers

Usage:
1. Copy this template
2. Replace TOOL_NAME, FUNCTION_NAME with your values
3. Implement your tool functions
4. Build and deploy container
"""

import asyncio
import sys
import os
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from datetime import datetime
from typing import List, Optional, Dict, Any

# =============================================================================
# CONFIGURATION - CUSTOMIZE THESE VALUES
# =============================================================================

TOOL_NAME = "example_tool"  # Change this to your tool name
TOOL_DESCRIPTION = "Example tool for demonstration. Use this tool when users ask to process example data."
TOOL_VERSION = "1.0.0"
SERVER_PORT = 8002  # Change if needed

# =============================================================================
# FASTAPI APP SETUP
# =============================================================================

app = FastAPI(
    title="MCP OpenAPI Proxy",
    description=f"""Automatically generated API from MCP Tool Schemas

- **available tools**：
    - [{TOOL_NAME}](/{TOOL_NAME}/docs)""",
    version="1.0"
)

# Add CORS middleware - REQUIRED for Open WebUI
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =============================================================================
# REQUEST/RESPONSE MODELS - CUSTOMIZE FOR YOUR TOOL
# =============================================================================

class ExampleRequest(BaseModel):
    """Example request model - customize for your tool"""
    text: str
    max_length: Optional[int] = 1000
    options: Optional[List[str]] = []

class BatchRequest(BaseModel):
    """Batch processing request model"""
    items: List[str]
    batch_size: Optional[int] = 10

# =============================================================================
# CORE ENDPOINTS
# =============================================================================

@app.get("/")
async def root():
    """Root endpoint - shows available tools"""
    return {
        "openapi": "3.1.0",
        "info": {
            "title": "MCP OpenAPI Proxy",
            "description": f"Automatically generated API from MCP Tool Schemas\n\n- **available tools**：\n    - [{TOOL_NAME}](/{TOOL_NAME}/docs)",
            "version": "1.0"
        },
        "paths": {}
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "tools": [TOOL_NAME],
        "timestamp": datetime.now().isoformat()
    }

# =============================================================================
# TOOL DOCUMENTATION ENDPOINT
# =============================================================================

@app.get(f"/{TOOL_NAME}/docs", response_class=HTMLResponse)
async def tool_docs():
    """Swagger UI for the tool"""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
    <link type="text/css" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css">
    <link rel="shortcut icon" href="https://fastapi.tiangolo.com/img/favicon.png">
    <title>{TOOL_NAME} - Swagger UI</title>
    </head>
    <body>
    <div id="swagger-ui">
    </div>
    <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js"></script>
    <script>
    const ui = SwaggerUIBundle({{
        url: '/{TOOL_NAME}/openapi.json',
        "dom_id": "#swagger-ui",
        "layout": "BaseLayout",
        "deepLinking": true,
        "showExtensions": true,
        "showCommonExtensions": true
    }})
    </script>
    </body>
    </html>
    """

# =============================================================================
# OPENAPI SPECIFICATION - CUSTOMIZE FOR YOUR TOOL
# =============================================================================

@app.get(f"/{TOOL_NAME}/openapi.json")
async def tool_openapi():
    """OpenAPI specification for the tool"""
    return {
        "openapi": "3.1.0",
        "info": {
            "title": TOOL_NAME,
            "description": TOOL_DESCRIPTION,
            "version": TOOL_VERSION
        },
        "paths": {
            "/process": {
                "post": {
                    "summary": "Process example data",
                    "description": "Use this function when users ask to process example data or text.",
                    "operationId": "process_example",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "text": {
                                            "type": "string",
                                            "description": "Text to process",
                                            "example": "Hello world"
                                        },
                                        "max_length": {
                                            "type": "integer",
                                            "description": "Maximum length of result",
                                            "default": 1000
                                        },
                                        "options": {
                                            "type": "array",
                                            "items": {"type": "string"},
                                            "description": "Processing options",
                                            "default": []
                                        }
                                    },
                                    "required": ["text"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successfully processed data",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "result": {"type": "string"},
                                            "metadata": {"type": "object"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/batch_process": {
                "post": {
                    "summary": "Process multiple items",
                    "description": "Use this function when users ask to process multiple items at once.",
                    "operationId": "batch_process",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "items": {
                                            "type": "array",
                                            "items": {"type": "string"},
                                            "description": "List of items to process"
                                        },
                                        "batch_size": {
                                            "type": "integer",
                                            "description": "Batch size for processing",
                                            "default": 10
                                        }
                                    },
                                    "required": ["items"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Batch processing results",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "results": {"type": "array"},
                                            "total_processed": {"type": "integer"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

# =============================================================================
# TOOL FUNCTION IMPLEMENTATIONS - CUSTOMIZE THESE
# =============================================================================

@app.post(f"/{TOOL_NAME}/process")
async def process_example(request: ExampleRequest):
    """
    Process example data - CUSTOMIZE THIS FUNCTION
    
    This is where you implement your actual tool logic.
    Replace this with your real implementation.
    """
    try:
        # Example implementation - replace with your logic
        processed_text = f"Processed: {request.text}"
        
        # Apply max_length if specified
        if request.max_length and len(processed_text) > request.max_length:
            processed_text = processed_text[:request.max_length] + "..."
        
        # Example metadata
        metadata = {
            "original_length": len(request.text),
            "processed_length": len(processed_text),
            "options_used": request.options,
            "processing_time": 0.1
        }
        
        return {
            "success": True,
            "result": processed_text,
            "metadata": metadata
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "result": None
        }

@app.post(f"/{TOOL_NAME}/batch_process")
async def batch_process(request: BatchRequest):
    """
    Batch process multiple items - CUSTOMIZE THIS FUNCTION
    """
    try:
        results = []
        
        # Process each item
        for i, item in enumerate(request.items):
            try:
                # Example processing - replace with your logic
                processed_item = f"Batch processed [{i+1}]: {item}"
                results.append({
                    "index": i,
                    "original": item,
                    "processed": processed_item,
                    "success": True
                })
            except Exception as e:
                results.append({
                    "index": i,
                    "original": item,
                    "error": str(e),
                    "success": False
                })
        
        return {
            "success": True,
            "results": results,
            "total_processed": len(results),
            "batch_size": request.batch_size
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "results": []
        }

# =============================================================================
# SERVER STARTUP
# =============================================================================

if __name__ == "__main__":
    print(f"🚀 Starting {TOOL_NAME} MCPO Server on port {SERVER_PORT}")
    print(f"📋 Compatible with Open WebUI tool system")
    print(f"🔗 OpenAPI spec: http://localhost:{SERVER_PORT}/{TOOL_NAME}/openapi.json")
    print(f"📖 API docs: http://localhost:{SERVER_PORT}/{TOOL_NAME}/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=SERVER_PORT)
