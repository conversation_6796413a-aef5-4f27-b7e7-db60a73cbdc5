version: '3.8'

services:
  jina-crawler-http-8001:
    build:
      context: .
      dockerfile: Dockerfile.jina-http-8001
    container_name: jina-crawler-http-8001
    ports:
      - "8001:8001"
    networks:
      - acca-network
      - unified-mcpo-network
      - gemini-network
    environment:
      - GEMINI_API_KEY=AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM
      - PYTHONPATH=/app/mcp-integration/servers/jina_crawler
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s

networks:
  acca-network:
    external: true
  unified-mcpo-network:
    external: true
  gemini-network:
    external: true
