FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir \
    fastapi==0.104.1 \
    uvicorn==0.24.0 \
    pydantic==2.5.0 \
    httpx \
    aiohttp \
    beautifulsoup4 \
    lxml \
    google-generativeai \
    duckduckgo-search \
    requests \
    tenacity \
    tls-client \
    fake-useragent \
    selenium \
    undetected-chromedriver \
    playwright

# Copy the MCP integration and proxy server
COPY mcp-integration/ ./mcp-integration/
COPY jina_crawler_mcp_proxy_server.py .

# Expose port
EXPOSE 8002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/health || exit 1

# Start command
CMD ["python", "jina_crawler_mcp_proxy_server.py"]
