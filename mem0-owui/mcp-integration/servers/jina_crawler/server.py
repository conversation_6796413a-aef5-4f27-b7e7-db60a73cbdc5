#!/usr/bin/env python3
"""
MCP Server for Jina Crawler - <PERSON>ân theo chuẩn MCP Protocol
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)

# Import the existing crawler components
from jini_crawler import JiniCrawler
from paywall_bypass_crawler import PaywallBypassCrawler
from ai_search.ai_search_engine import AISearchEngine

class JinaCrawlerMCPServer:
    """MCP Server for Jina Crawler with all 10 tools"""
    
    def __init__(self):
        self.server = Server("jina-crawler")
        self.crawler = JiniCrawler()
        self.paywall_crawler = PaywallBypassCrawler()
        self.ai_search_engine = AISearchEngine()
        self.initialized = False
        
        # Register tools
        self._register_tools()
        
        # Register resources
        self._register_resources()
        
        # Register handlers
        self._register_handlers()
    
    def _register_tools(self):
        """Register all MCP tools"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List all available tools"""
            return [
                Tool(
                    name="crawl_url",
                    description="Smart summarizer with AI-powered content processing",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "URL to crawl"},
                            "max_content_length": {"type": "number", "default": 10000, "description": "Maximum content length"}
                        },
                        "required": ["url"]
                    }
                ),
                Tool(
                    name="crawl_full_article",
                    description="Complete article extractor without summarization",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "URL to extract complete content"},
                            "max_content_length": {"type": "number", "default": 50000, "description": "Maximum content length"}
                        },
                        "required": ["url"]
                    }
                ),
                Tool(
                    name="crawl_batch",
                    description="Crawl multiple URLs in parallel",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "urls": {"type": "array", "items": {"type": "string"}, "description": "List of URLs to crawl"},
                            "max_content_length": {"type": "number", "default": 10000, "description": "Maximum content length per URL"}
                        },
                        "required": ["urls"]
                    }
                ),
                Tool(
                    name="search_site",
                    description="Search within a specific website",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "site_url": {"type": "string", "description": "Base URL of site"},
                            "query": {"type": "string", "description": "Search query"},
                            "max_results": {"type": "number", "default": 10, "description": "Maximum number of results"}
                        },
                        "required": ["site_url", "query"]
                    }
                ),
                Tool(
                    name="health_check",
                    description="Check crawler health status",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                Tool(
                    name="get_crawler_stats",
                    description="Get performance statistics",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                ),
                Tool(
                    name="crawl_bypass_paywall",
                    description="🔓 Bypass paywall and extract content",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "Paywall-protected URL"},
                            "max_content_length": {"type": "number", "default": 50000, "description": "Maximum content length"}
                        },
                        "required": ["url"]
                    }
                ),
                Tool(
                    name="ai_search",
                    description="🤖 AI Search Engine with Brave support",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "enable_query_refinement": {"type": "boolean", "default": True, "description": "Enable query refinement"},
                            "search_type": {"type": "string", "enum": ["text", "news"], "default": "text", "description": "Type of search"},
                            "search_engine": {"type": "string", "enum": ["duckduckgo", "brave"], "default": "duckduckgo", "description": "Search engine to use"},
                            "max_sources": {"type": "number", "default": 10, "description": "Maximum number of sources"}
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="ai_search_streaming",
                    description="🚀 AI Search with real-time updates",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "enable_query_refinement": {"type": "boolean", "default": True, "description": "Enable query refinement"}
                        },
                        "required": ["query"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool execution"""
            await self._ensure_initialized()
            
            try:
                if name == "crawl_url":
                    url = arguments["url"]
                    max_content_length = arguments.get("max_content_length", 10000)
                    
                    result = await self.crawler.crawl_and_process(url, max_content_length)
                    
                    response = {
                        "success": result.success,
                        "url": result.url,
                        "title": result.title,
                        "processed_content": result.processed_content,
                        "processing_time": result.processing_time,
                        "error": result.error,
                        "metadata": result.metadata,
                        "crawler_type": "jina_style_with_gemini"
                    }
                    
                elif name == "crawl_full_article":
                    url = arguments["url"]
                    max_content_length = arguments.get("max_content_length", 50000)
                    
                    result = await self.crawler.crawl_full_article(url, max_content_length)
                    
                    response = {
                        "success": result.success,
                        "url": result.url,
                        "title": result.title,
                        "full_article_content": result.processed_content,
                        "processing_time": result.processing_time,
                        "error": result.error,
                        "metadata": result.metadata,
                        "crawler_type": "jina_full_article",
                        "content_type": "complete_article"
                    }
                    
                elif name == "crawl_batch":
                    urls = arguments["urls"]
                    max_content_length = arguments.get("max_content_length", 10000)
                    
                    # Process URLs in parallel
                    tasks = []
                    for url in urls:
                        task = self.crawler.crawl_and_process(url, max_content_length)
                        tasks.append(task)
                    
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # Format results
                    formatted_results = []
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            formatted_results.append({
                                "success": False,
                                "url": urls[i],
                                "error": str(result)
                            })
                        else:
                            formatted_results.append({
                                "success": result.success,
                                "url": result.url,
                                "title": result.title,
                                "processed_content": result.processed_content,
                                "processing_time": result.processing_time,
                                "error": result.error,
                                "metadata": result.metadata
                            })
                    
                    response = {
                        "batch_size": len(urls),
                        "successful_crawls": sum(1 for r in formatted_results if r["success"]),
                        "failed_crawls": sum(1 for r in formatted_results if not r["success"]),
                        "results": formatted_results,
                        "crawler_type": "jina_style_batch"
                    }
                    
                elif name == "search_site":
                    site_url = arguments["site_url"]
                    query = arguments["query"]
                    max_results = arguments.get("max_results", 10)
                    
                    try:
                        # Simple implementation using jini_crawler
                        search_url = f"{site_url}/search?q={query}"
                        result = await self.crawler.crawl_and_process(search_url, 5000)
                        
                        response = {
                            "site_url": site_url,
                            "query": query,
                            "success": result.success,
                            "total_found": 1 if result.success else 0,
                            "results": [{"title": result.title, "url": result.url, "snippet": result.processed_content[:200]}] if result.success else [],
                            "error": result.error,
                            "search_type": "site_search_via_jini_crawler"
                        }
                    except Exception as e:
                        response = {
                            "site_url": site_url,
                            "query": query,
                            "success": False,
                            "total_found": 0,
                            "results": [],
                            "error": str(e),
                            "search_type": "site_search_via_jini_crawler"
                        }
                        
                elif name == "health_check":
                    health = await self.crawler.health_check()
                    
                    response = {
                        "health_check": health,
                        "server_status": "operational" if health.get("status") == "healthy" else "degraded"
                    }
                    
                elif name == "get_crawler_stats":
                    response = {
                        "server_initialized": self.initialized,
                        "crawler_type": "jina_style_with_gemini",
                        "features": [
                            "aiohttp-based crawling",
                            "BeautifulSoup HTML cleaning", 
                            "Gemini 2.5 Flash Lite processing",
                            "Vietnamese content optimization",
                            "Async parallel processing",
                            "Brave Search fallback",
                            "Paywall bypass (8 techniques)",
                            "AI Search streaming"
                        ]
                    }
                    
                elif name == "crawl_bypass_paywall":
                    url = arguments["url"]
                    max_content_length = arguments.get("max_content_length", 50000)
                    
                    result = await self.paywall_crawler.crawl_with_paywall_bypass(url, max_content_length)
                    
                    response = {
                        "success": result.success,
                        "url": result.url,
                        "title": result.title,
                        "full_article_content": result.processed_content,
                        "processing_time": result.processing_time,
                        "error": result.error,
                        "metadata": result.metadata,
                        "crawler_type": "paywall_bypass",
                        "bypass_method": result.metadata.get("bypass_method", "unknown") if result.metadata else "unknown",
                        "content_type": "paywall_bypassed_article"
                    }
                    
                elif name == "ai_search":
                    query = arguments["query"]
                    enable_query_refinement = arguments.get("enable_query_refinement", True)
                    search_type = arguments.get("search_type", "text")
                    max_sources = arguments.get("max_sources", 10)

                    # Update AI search engine max results if needed
                    self.ai_search_engine.max_search_results = max_sources

                    try:
                        result = await self.ai_search_engine.search(
                            query=query,
                            enable_query_refinement=enable_query_refinement,
                            search_type=search_type
                        )
                    except Exception as e:
                        # Ensure cleanup even on error
                        try:
                            await self.ai_search_engine.cleanup()
                        except:
                            pass
                        raise

                    if result.success and result.synthesized_answer:
                        response = {
                            "success": True,
                            "query": result.query,
                            "refined_query": result.refined_query.refined_query if result.refined_query else None,
                            "answer": result.synthesized_answer.answer,
                            "citations": [
                                {
                                    "url": citation.url,
                                    "title": citation.title,
                                    "snippet": citation.snippet,
                                    "relevance_score": citation.relevance_score
                                }
                                for citation in result.synthesized_answer.citations
                            ],
                            "confidence": result.synthesized_answer.confidence,
                            "sources_used": result.synthesized_answer.sources_used,
                            "word_count": result.synthesized_answer.word_count,
                            "total_time": result.total_time,
                            "metadata": result.metadata,
                            "search_type": "ai_search_perplexity_style"
                        }
                    else:
                        response = {
                            "success": False,
                            "query": result.query,
                            "error": result.error,
                            "total_time": result.total_time,
                            "metadata": result.metadata,
                            "search_type": "ai_search_perplexity_style"
                        }
                        
                elif name == "ai_search_streaming":
                    query = arguments["query"]
                    enable_query_refinement = arguments.get("enable_query_refinement", True)

                    # For streaming, we'll collect updates and return them all at once
                    updates = []

                    async def collect_updates(update):
                        updates.append(update)

                    await self.ai_search_engine.search_streaming(
                        query=query,
                        callback=collect_updates,
                        enable_query_refinement=enable_query_refinement
                    )

                    response = {
                        "success": True,
                        "query": query,
                        "streaming_updates": updates,
                        "search_type": "ai_search_streaming"
                    }
                    
                else:
                    raise ValueError(f"Unknown tool: {name}")
                
                return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]
                
            except Exception as e:
                error_response = {
                    "success": False,
                    "error": str(e),
                    "tool": name
                }
                return [TextContent(type="text", text=json.dumps(error_response, indent=2, ensure_ascii=False))]
    
    def _register_resources(self):
        """Register MCP resources"""
        
        @self.server.list_resources()
        async def handle_list_resources() -> List[Resource]:
            """List available resources"""
            return [
                Resource(
                    uri="jina://crawler/stats",
                    name="Crawler Statistics",
                    description="Current crawler performance statistics",
                    mimeType="application/json"
                ),
                Resource(
                    uri="jina://crawler/health",
                    name="Health Status",
                    description="Current health status of the crawler",
                    mimeType="application/json"
                )
            ]
        
        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> str:
            """Read resource content"""
            await self._ensure_initialized()
            
            if uri == "jina://crawler/stats":
                stats = {
                    "server_initialized": self.initialized,
                    "crawler_type": "jina_style_with_gemini",
                    "features": [
                        "aiohttp-based crawling",
                        "BeautifulSoup HTML cleaning", 
                        "Gemini 2.5 Flash Lite processing",
                        "Vietnamese content optimization",
                        "Async parallel processing",
                        "Brave Search fallback",
                        "Paywall bypass (8 techniques)",
                        "AI Search streaming"
                    ]
                }
                return json.dumps(stats, indent=2, ensure_ascii=False)
                
            elif uri == "jina://crawler/health":
                health = await self.crawler.health_check()
                return json.dumps(health, indent=2, ensure_ascii=False)
                
            else:
                raise ValueError(f"Unknown resource: {uri}")
    
    def _register_handlers(self):
        """Register MCP handlers"""
        
        @self.server.set_logging_level()
        async def handle_set_logging_level(level: LoggingLevel) -> None:
            """Set logging level"""
            pass
    
    async def _ensure_initialized(self):
        """Ensure all components are initialized"""
        if not self.initialized:
            await self.crawler.initialize()
            await self.paywall_crawler.initialize()
            await self.ai_search_engine.initialize()
            self.initialized = True
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.initialized:
            await self.crawler.cleanup()
            await self.paywall_crawler.cleanup()
            await self.ai_search_engine.cleanup()
            self.initialized = False

async def main():
    """Main entry point for MCP server"""
    mcp_server = JinaCrawlerMCPServer()
    
    try:
        # Run the MCP server
        async with stdio_server() as (read_stream, write_stream):
            await mcp_server.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="jina-crawler",
                    server_version="1.0.0",
                    capabilities=mcp_server.server.get_capabilities()
                )
            )
    finally:
        await mcp_server.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
