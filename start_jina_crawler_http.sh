#!/bin/bash

# Start Jina Crawler HTTP Server
# This script starts the HTTP wrapper for Jina Crawler to work with Open WebUI

echo "🚀 Starting Jina Crawler HTTP Server..."

# Set environment variables
export PYTHONPATH="/home/<USER>/AccA/AccA/mcp-integration/servers/jina_crawler:$PYTHONPATH"
export GEMINI_API_KEY="AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"

# Check if virtual environment exists
if [ -d "mcp-integration/servers/jina_crawler/venv" ]; then
    echo "📦 Activating virtual environment..."
    source mcp-integration/servers/jina_crawler/venv/bin/activate
else
    echo "⚠️  Virtual environment not found, using system Python"
fi

# Install required packages if needed
echo "📋 Installing required packages..."
pip install fastapi uvicorn pydantic

# Start the server
echo "🌐 Starting server on http://0.0.0.0:8001"
python3 jina_crawler_http_server.py

echo "✅ Jina Crawler HTTP Server started successfully!"
echo "📍 Available at: http://localhost:8001"
echo "📖 API docs at: http://localhost:8001/docs"
echo "🔧 Health check: http://localhost:8001/health"
