FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for Jina Crawler
RUN pip install --no-cache-dir \
    fastapi \
    uvicorn \
    pydantic \
    aiohttp \
    beautifulsoup4 \
    requests \
    tls-client \
    fake-useragent

# Copy Jina Crawler source code
COPY mcp-integration/servers/jina_crawler/ ./jina_crawler/

# Copy HTTP server
COPY jina_crawler_http_server.py .

# Set Python path
ENV PYTHONPATH="/app/jina_crawler:$PYTHONPATH"

# Set Gemini API key
ENV GEMINI_API_KEY="AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Start command
CMD ["python", "jina_crawler_http_server.py"]
