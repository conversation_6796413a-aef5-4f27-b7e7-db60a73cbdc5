#!/bin/bash

# Complete startup script for Jina Crawler + Open WebUI integration
# This script starts both services and configures them to work together

echo "🚀 Starting Jina Crawler + Open WebUI Integration"
echo "=================================================="

# Set environment variables
export PYTHONPATH="/home/<USER>/AccA/AccA/mcp-integration/servers/jina_crawler:$PYTHONPATH"
export GEMINI_API_KEY="AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"

# Function to check if a service is running
check_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    echo "🔍 Checking $name..."
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo "✅ $name is running"
            return 0
        fi
        echo "⏳ Waiting for $name... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $name failed to start after $max_attempts attempts"
    return 1
}

# Function to start Jina Crawler HTTP Server
start_jina_crawler() {
    echo "🌐 Starting Jina Crawler HTTP Server..."
    
    # Check if already running
    if curl -s "http://localhost:8001/health" > /dev/null 2>&1; then
        echo "✅ Jina Crawler HTTP Server is already running"
        return 0
    fi
    
    # Install dependencies if needed
    echo "📦 Installing dependencies..."
    pip install fastapi uvicorn pydantic aiohttp beautifulsoup4 requests tls-client fake-useragent > /dev/null 2>&1
    
    # Start the server in background
    nohup python3 jina_crawler_http_server.py > jina_crawler_http.log 2>&1 &
    local pid=$!
    echo "🔄 Started Jina Crawler HTTP Server (PID: $pid)"
    
    # Wait for service to be ready
    if check_service "http://localhost:8001/health" "Jina Crawler HTTP Server"; then
        echo "✅ Jina Crawler HTTP Server started successfully"
        return 0
    else
        echo "❌ Failed to start Jina Crawler HTTP Server"
        return 1
    fi
}

# Function to check Open WebUI
check_openwebui() {
    echo "🔍 Checking Open WebUI..."
    
    # Try common ports
    local ports=(3000 8080 80)
    for port in "${ports[@]}"; do
        if curl -s "http://localhost:$port" > /dev/null 2>&1; then
            echo "✅ Open WebUI is running on port $port"
            return 0
        fi
    done
    
    echo "❌ Open WebUI is not running on common ports"
    echo "📋 Please start Open WebUI first"
    return 1
}

# Function to configure integration
configure_integration() {
    echo "🔧 Configuring Open WebUI integration..."
    
    # Run configuration script
    python3 configure_openwebui_jina_http.py
    
    if [ $? -eq 0 ]; then
        echo "✅ Integration configured successfully"
        return 0
    else
        echo "❌ Failed to configure integration"
        return 1
    fi
}

# Function to test the integration
test_integration() {
    echo "🧪 Testing integration..."
    
    # Test Jina Crawler endpoint
    local test_response=$(curl -s -X POST "http://localhost:8001/jina_crawler/crawl" \
        -H "Content-Type: application/json" \
        -d '{"url": "https://example.com", "max_content_length": 1000}')
    
    if echo "$test_response" | grep -q '"success":true'; then
        echo "✅ Jina Crawler test successful"
        return 0
    else
        echo "❌ Jina Crawler test failed"
        echo "Response: $test_response"
        return 1
    fi
}

# Function to show status
show_status() {
    echo ""
    echo "📊 Service Status:"
    echo "=================="
    
    # Check Jina Crawler
    if curl -s "http://localhost:8001/health" > /dev/null 2>&1; then
        echo "✅ Jina Crawler HTTP Server: http://localhost:8001"
        echo "📖 API Documentation: http://localhost:8001/docs"
    else
        echo "❌ Jina Crawler HTTP Server: Not running"
    fi
    
    # Check Open WebUI
    local ports=(3000 8080 80)
    local openwebui_found=false
    for port in "${ports[@]}"; do
        if curl -s "http://localhost:$port" > /dev/null 2>&1; then
            echo "✅ Open WebUI: http://localhost:$port"
            openwebui_found=true
            break
        fi
    done
    
    if [ "$openwebui_found" = false ]; then
        echo "❌ Open WebUI: Not running"
    fi
    
    echo ""
    echo "📋 Next Steps:"
    echo "=============="
    echo "1. Go to Open WebUI Admin Panel > Functions"
    echo "2. Create a new function and paste code from 'openwebui_jina_crawler_function.py'"
    echo "3. Enable the function and test it with a URL"
    echo "4. Use the function in your chats: jina_crawler_tool('https://example.com')"
}

# Main execution
main() {
    # Step 1: Start Jina Crawler HTTP Server
    if ! start_jina_crawler; then
        echo "❌ Failed to start Jina Crawler HTTP Server"
        exit 1
    fi
    
    # Step 2: Check Open WebUI
    if ! check_openwebui; then
        echo "❌ Open WebUI is not running"
        echo "📋 Please start Open WebUI and run this script again"
        exit 1
    fi
    
    # Step 3: Configure integration
    if ! configure_integration; then
        echo "❌ Failed to configure integration"
        exit 1
    fi
    
    # Step 4: Test integration
    if ! test_integration; then
        echo "⚠️  Integration test failed, but services are running"
    fi
    
    # Step 5: Show status
    show_status
    
    echo ""
    echo "🎉 Jina Crawler + Open WebUI integration is ready!"
    echo "🔗 The services are now connected and ready to use"
}

# Handle script termination
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    # Kill background processes if needed
    pkill -f "jina_crawler_http_server.py" 2>/dev/null
    echo "✅ Cleanup completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Run main function
main

# Keep script running to monitor services
echo ""
echo "🔄 Services are running. Press Ctrl+C to stop."
echo "📊 Monitor logs: tail -f jina_crawler_http.log"

# Wait for user interrupt
while true; do
    sleep 10
    # Check if services are still running
    if ! curl -s "http://localhost:8001/health" > /dev/null 2>&1; then
        echo "❌ Jina Crawler HTTP Server stopped unexpectedly"
        break
    fi
done
