version: '3.8'

services:
  jina-crawler-mcp-proxy:
    build:
      context: .
      dockerfile: Dockerfile.jina-mcp-proxy
    container_name: jina-crawler-mcp-proxy-8002
    ports:
      - "8002:8002"
    networks:
      - acca-network
      - unified-mcpo-network
      - gemini-network
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  acca-network:
    external: true
  unified-mcpo-network:
    external: true
  gemini-network:
    external: true
