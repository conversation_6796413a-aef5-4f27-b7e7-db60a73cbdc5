version: '3.8'

services:
  jina-crawler-http:
    build:
      context: .
      dockerfile: Dockerfile.jina-crawler-http
    container_name: jina-crawler-http-server
    ports:
      - "8001:8001"
    networks:
      - acca-network
      - unified-mcpo-network
      - gemini-network
    environment:
      - GEMINI_API_KEY=AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM
      - PYTHONPATH=/app/jina_crawler
    volumes:
      - ./mcp-integration/servers/jina_crawler:/app/jina_crawler:ro
      - ./jina_crawler_http_server.py:/app/jina_crawler_http_server.py:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  acca-network:
    external: true
  unified-mcpo-network:
    external: true
  gemini-network:
    external: true
