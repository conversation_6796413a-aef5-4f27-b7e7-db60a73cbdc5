#!/usr/bin/env python3
"""
Custom MCPO Server with Individual Tool Path Exposure
Aggregates all MCP server tools into main OpenAPI schema
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, Any, List
import httpx
from fastapi import FastAP<PERSON>, HTTPException, Depends, Security, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()
API_KEY = "acca-enhanced-rag-mcp-key-2025"

def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)):
    if credentials.credentials != API_KEY:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return credentials.credentials

class CustomMCPOServer:
    def __init__(self, config_path: str):
        self.app = FastAPI(
            title="Enhanced MCPO Server",
            description="MCPO Server with Individual Tool Path Exposure",
            version="1.0.0",
            docs_url=None,
            redoc_url=None,
            openapi_url=None
        )
        self.config_path = config_path
        self.mcp_servers = {}
        self.tool_paths = {}
        
        # Add CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self.setup_routes()
    
    def load_config(self):
        """Load MCP server configuration"""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            
            self.mcp_servers = config.get('servers', {})
            logger.info(f"Loaded {len(self.mcp_servers)} MCP servers from config")
            
            for name, server_config in self.mcp_servers.items():
                logger.info(f"  - {name}: {server_config.get('url', 'N/A')}")
                
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            self.mcp_servers = {}
    
    async def fetch_server_tools(self, server_name: str, server_config: Dict) -> Dict:
        """Fetch tools from an MCP server"""
        try:
            url = server_config.get('url', '')
            if not url:
                return {}
            
            # Try to get OpenAPI schema
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{url}/openapi.json")
                if response.status_code == 200:
                    schema = response.json()
                    paths = schema.get('paths', {})
                    
                    # Store tools with server prefix
                    server_tools = {}
                    for path, methods in paths.items():
                        # Add server prefix to path
                        prefixed_path = f"/{server_name}{path}"
                        server_tools[prefixed_path] = methods
                        
                        # Update server references
                        for method, details in methods.items():
                            if 'tags' not in details:
                                details['tags'] = [server_name]
                            details['x-server-name'] = server_name
                            details['x-original-path'] = path
                    
                    logger.info(f"Fetched {len(server_tools)} tools from {server_name}")
                    return server_tools
                    
        except Exception as e:
            logger.error(f"Failed to fetch tools from {server_name}: {e}")
        
        return {}
    
    async def aggregate_all_tools(self) -> Dict:
        """Aggregate tools from all MCP servers"""
        all_tools = {}
        
        for server_name, server_config in self.mcp_servers.items():
            server_tools = await self.fetch_server_tools(server_name, server_config)
            all_tools.update(server_tools)
        
        logger.info(f"Aggregated {len(all_tools)} total tool paths")
        return all_tools
    
    def setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/")
        async def root():
            return {"message": "Enhanced MCPO Server", "version": "1.0.0"}
        
        @self.app.get("/health")
        async def health():
            return {"status": "healthy", "servers": len(self.mcp_servers)}
        
        @self.app.get("/openapi.json")
        async def get_openapi():
            """Generate aggregated OpenAPI schema with all tools"""
            
            # Base OpenAPI schema
            openapi_schema = {
                "openapi": "3.1.0",
                "info": {
                    "title": "Enhanced MCPO Server",
                    "description": "Aggregated MCP Tools with Individual Path Exposure",
                    "version": "1.0.0"
                },
                "servers": [{"url": "/"}],
                "paths": {},
                "components": {
                    "securitySchemes": {
                        "HTTPBearer": {
                            "type": "http",
                            "scheme": "bearer"
                        }
                    }
                },
                "security": [{"HTTPBearer": []}]
            }
            
            # Add aggregated tool paths
            tool_paths = await self.aggregate_all_tools()
            openapi_schema["paths"] = tool_paths
            
            return openapi_schema
        
        @self.app.get("/servers")
        async def list_servers():
            """List all configured MCP servers"""
            return {
                "servers": list(self.mcp_servers.keys()),
                "total": len(self.mcp_servers)
            }
        
        # Proxy requests to individual servers
        @self.app.api_route("/{server_name}/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
        async def proxy_to_server(
            server_name: str, 
            path: str,
            request: Request,
            api_key: str = Depends(verify_api_key)
        ):
            """Proxy requests to individual MCP servers"""
            
            if server_name not in self.mcp_servers:
                raise HTTPException(status_code=404, detail=f"Server {server_name} not found")
            
            server_config = self.mcp_servers[server_name]
            server_url = server_config.get('url', '')
            
            if not server_url:
                raise HTTPException(status_code=500, detail=f"No URL configured for {server_name}")
            
            try:
                # Get request body. Some FastAPI routes might expect JSON body; ensure proper content-type handling
                raw_body = await request.body()
                body = raw_body if raw_body else None
                
                # Forward request to MCP server
                async with httpx.AsyncClient(timeout=30.0) as client:
                    # Prepare headers and inject downstream Authorization if configured
                    # Copy headers except any existing Authorization to avoid duplicates
                    forward_headers = {k: v for k, v in request.headers.items() if k.lower() != 'authorization'}
                    server_api_key = self.mcp_servers.get(server_name, {}).get('api_key')
                    if server_api_key:
                        forward_headers['Authorization'] = f"Bearer {server_api_key}"
                    # For GET without a body, avoid sending content to prevent 422s
                    if request.method.upper() == "GET" and body is None:
                        response = await client.request(
                            method=request.method,
                            url=f"{server_url}/{path}",
                            headers=forward_headers,
                        )
                    else:
                        response = await client.request(
                            method=request.method,
                            url=f"{server_url}/{path}",
                            headers=forward_headers,
                            content=body
                        )
                    
                    if response.headers.get('content-type', '').startswith('application/json'):
                        return JSONResponse(content=response.json(), status_code=response.status_code)
                    return JSONResponse(content=response.text, status_code=response.status_code)
                    
            except Exception as e:
                logger.error(f"Proxy error for {server_name}/{path}: {e}")
                raise HTTPException(status_code=500, detail=f"Proxy error: {str(e)}")
    
    async def startup(self):
        """Startup tasks"""
        self.load_config()
        logger.info("Enhanced MCPO Server started successfully")
    
    def run(self, host: str = "0.0.0.0", port: int = 5000):
        """Run the server"""
        # Load configuration before starting the server (avoid creating tasks without a running loop)
        self.load_config()
        uvicorn.run(self.app, host=host, port=port)

if __name__ == "__main__":
    import sys
    import os
    
    config_path = sys.argv[1] if len(sys.argv) > 1 else "config/mcpo_config.json"
    server = CustomMCPOServer(config_path)
    # Allow overriding port via env var PORT or second CLI arg
    default_port = 5000
    port_env = os.environ.get("PORT")
    if port_env and port_env.isdigit():
        selected_port = int(port_env)
    elif len(sys.argv) > 2 and sys.argv[2].isdigit():
        selected_port = int(sys.argv[2])
    else:
        selected_port = default_port
    server.run(port=selected_port)