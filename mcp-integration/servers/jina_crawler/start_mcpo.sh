#!/bin/bash

# Start MCPO for Jina Crawler on port 8009
# This script follows the MCPO standard from https://github.com/open-webui/mcpo

echo "🚀 Starting MCPO for Jina Crawler on port 8009..."

# Set working directory
cd /home/<USER>/AccA/AccA/mcp-integration/servers/jina_crawler

# Install MCP dependencies if needed
pip install --break-system-packages mcp || echo "MCP already installed"

# Stop existing container if running
docker stop jina-crawler-8009 2>/dev/null || true
docker rm jina-crawler-8009 2>/dev/null || true

# Start MCPO with config file
uvx mcpo \
  --port 8009 \
  --api-key "jina-crawler-secret-key-2025" \
  --config mcpo_config.json \
  --host 0.0.0.0

echo "✅ MCPO for Jina Crawler started on http://localhost:8009"
echo "🔑 API Key: jina-crawler-secret-key-2025"
echo "📚 Docs: http://localhost:8009/docs"
echo "🔍 OpenAPI: http://localhost:8009/openapi.json"