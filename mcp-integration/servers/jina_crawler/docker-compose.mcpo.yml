version: '3.8'

services:
  jina-crawler-mcpo:
    build:
      context: .
      dockerfile: Dockerfile.mcpo
    container_name: jina-crawler-mcpo-8009
    ports:
      - "8009:8009"
    environment:
      - PYTHONPATH=/app
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - BRAVE_API_KEY=${BRAVE_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    networks:
      - jina-mcpo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8009/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  jina-mcpo-network:
    driver: bridge
    name: jina-mcpo-network

volumes:
  jina-mcpo-logs: