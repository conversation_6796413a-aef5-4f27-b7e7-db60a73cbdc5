#!/usr/bin/env python3
"""
MCPO-compatible MCP Server wrapper for Jina Crawler
Simplified version to work with MCPO without dependencies
"""

import asyncio
import json
import sys
from typing import Any, Dict, List
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Tool,
    TextContent,
)

class JinaMCPOWrapper:
    """Simplified Jina MCP Server for MCPO compatibility"""
    
    def __init__(self):
        self.server = Server("jina-crawler")
        self._register_tools()
    
    def _register_tools(self):
        """Register simplified Jina tools"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List all available tools"""
            return [
                Tool(
                    name="crawl_url",
                    description="Crawl and extract content from a URL",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string", 
                                "description": "URL to crawl"
                            },
                            "extract_media": {
                                "type": "boolean", 
                                "description": "Whether to extract media URLs", 
                                "default": False
                            }
                        },
                        "required": ["url"]
                    }
                ),
                Tool(
                    name="search_brave",
                    description="Search the web using Brave Search API",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string", 
                                "description": "Search query"
                            },
                            "count": {
                                "type": "integer", 
                                "description": "Number of results to return", 
                                "default": 5
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="summarize_content",
                    description="Summarize crawled content using AI",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "content": {
                                "type": "string", 
                                "description": "Content to summarize"
                            },
                            "max_length": {
                                "type": "integer", 
                                "description": "Maximum summary length", 
                                "default": 500
                            }
                        },
                        "required": ["content"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool execution - simplified responses for MCPO testing"""
            
            try:
                if name == "crawl_url":
                    url = arguments.get("url", "")
                    extract_media = arguments.get("extract_media", False)
                    
                    response = {
                        "success": True,
                        "url": url,
                        "title": f"Sample Title for {url}",
                        "content": f"Sample content extracted from {url}. This is a simplified response for MCPO testing.",
                        "extract_media": extract_media,
                        "media_urls": ["sample1.jpg", "sample2.png"] if extract_media else [],
                        "note": "This is a simplified response for MCPO compatibility testing"
                    }
                    
                elif name == "search_brave":
                    query = arguments.get("query", "")
                    count = arguments.get("count", 5)
                    
                    response = {
                        "success": True,
                        "query": query,
                        "results": [
                            {
                                "title": f"Sample Result {i+1} for: {query}",
                                "url": f"https://example{i+1}.com",
                                "description": f"Sample description {i+1} for query: {query}"
                            }
                            for i in range(min(count, 3))
                        ],
                        "note": "This is a simplified response for MCPO compatibility testing"
                    }
                    
                elif name == "summarize_content":
                    content = arguments.get("content", "")
                    max_length = arguments.get("max_length", 500)
                    
                    # Simple truncation as "summary"
                    summary = content[:max_length] + "..." if len(content) > max_length else content
                    
                    response = {
                        "success": True,
                        "original_length": len(content),
                        "summary_length": len(summary),
                        "summary": summary,
                        "note": "This is a simplified response for MCPO compatibility testing"
                    }
                    
                else:
                    response = {
                        "success": False,
                        "error": f"Unknown tool: {name}",
                        "available_tools": ["crawl_url", "search_brave", "summarize_content"]
                    }
                
                return [TextContent(type="text", text=json.dumps(response, indent=2))]
                
            except Exception as e:
                error_response = {
                    "success": False,
                    "error": str(e),
                    "tool": name
                }
                return [TextContent(type="text", text=json.dumps(error_response, indent=2))]

async def main():
    """Main entry point for MCP server"""
    mcp_server = JinaMCPOWrapper()
    
    # Run the MCP server
    async with stdio_server() as (read_stream, write_stream):
        await mcp_server.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="jina-crawler",
                server_version="1.0.0",
                capabilities=mcp_server.server.get_capabilities()
            )
        )

if __name__ == "__main__":
    asyncio.run(main())