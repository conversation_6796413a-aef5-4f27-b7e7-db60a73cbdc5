FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv for fast package management
RUN pip install uv

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements_mcp.txt .
RUN pip install --no-cache-dir -r requirements_mcp.txt

# Install MCPO
RUN pip install mcpo

# Copy application files
COPY . .

# Make scripts executable
RUN chmod +x start_mcpo.sh

# Expose port 8009
EXPOSE 8009

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8009/health || exit 1

# Start MCPO
CMD ["uvx", "mcpo", "--port", "8009", "--api-key", "jina-crawler-secret-key-2025", "--config", "mcpo_config.json", "--host", "0.0.0.0"]