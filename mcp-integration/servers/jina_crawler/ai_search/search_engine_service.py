"""
Search Engine Service

Provides integration with DuckDuckGo search engine to retrieve relevant URLs
for user queries. Supports both text and news search modes.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from duckduckgo_search import DDGS
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Represents a single search result"""
    title: str
    url: str
    snippet: str
    source: str = "duckduckgo"

@dataclass
class SearchResponse:
    """Represents the complete search response"""
    query: str
    results: List[SearchResult]
    total_results: int
    search_time: float
    success: bool
    error: Optional[str] = None

class SearchEngineService:
    """
    Service for searching the web using DuckDuckGo
    """
    
    def __init__(self, max_results: int = 10, timeout: int = 15):
        """
        Initialize the search engine service
        
        Args:
            max_results: Maximum number of search results to return
            timeout: Timeout for search requests in seconds
        """
        self.max_results = max_results
        self.timeout = timeout
        self.ddgs = None
        
    async def initialize(self):
        """Initialize the search engine"""
        try:
            # DuckDuckGo search doesn't require initialization
            # but we can use this for future enhancements
            logger.info("✅ Search Engine Service initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Search Engine Service: {e}")
            raise
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def search(
        self, 
        query: str, 
        search_type: str = "text",
        region: str = "wt-wt",
        safesearch: str = "moderate"
    ) -> SearchResponse:
        """
        Search for the given query using DuckDuckGo
        
        Args:
            query: Search query
            search_type: Type of search ("text", "news")
            region: Search region (default: worldwide)
            safesearch: Safe search setting ("strict", "moderate", "off")
            
        Returns:
            SearchResponse with results
        """
        import time
        start_time = time.time()
        
        try:
            logger.info(f"🔍 Searching DuckDuckGo for: '{query}'")
            
            # Run the search in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                None, 
                self._perform_search,
                query, search_type, region, safesearch
            )
            
            search_time = time.time() - start_time
            
            # Convert to SearchResult objects
            search_results = []
            for result in results[:self.max_results]:
                search_results.append(SearchResult(
                    title=result.get('title', ''),
                    url=result.get('href', ''),
                    snippet=result.get('body', ''),
                    source="duckduckgo"
                ))
            
            logger.info(f"✅ Found {len(search_results)} results in {search_time:.2f}s")
            
            return SearchResponse(
                query=query,
                results=search_results,
                total_results=len(search_results),
                search_time=search_time,
                success=True
            )
            
        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"❌ Search failed for query '{query}': {e}")
            
            return SearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                error=str(e)
            )
    
    def _perform_search(
        self,
        query: str,
        search_type: str,
        region: str,
        safesearch: str
    ) -> List[Dict[str, Any]]:
        """
        Perform the actual search (synchronous) with fallback strategies
        """
        import time

        # Try multiple search strategies if first one fails
        search_strategies = [
            query,  # Original query
            query.replace(":", "").replace("?", ""),  # Remove special chars
            " ".join(query.split()[:5]),  # Shorter query (first 5 words)
            query.split()[0] if query.split() else query  # Single keyword fallback
        ]

        for i, search_query in enumerate(search_strategies):
            try:
                with DDGS() as ddgs:
                    if search_type == "news":
                        results = list(ddgs.news(
                            keywords=search_query,
                            region=region,
                            safesearch=safesearch,
                            max_results=self.max_results
                        ))
                    else:
                        results = list(ddgs.text(
                            keywords=search_query,
                            region=region,
                            safesearch=safesearch,
                            max_results=self.max_results
                        ))

                    if results:  # If we got results, return them
                        if i > 0:  # Log if we used a fallback strategy
                            logger.info(f"🔄 Used fallback search strategy {i+1}: '{search_query}'")
                        return results

                    # If no results, try next strategy
                    if i < len(search_strategies) - 1:
                        time.sleep(0.5)  # Brief pause between attempts

            except Exception as e:
                logger.warning(f"⚠️ Search attempt {i+1} failed: {e}")
                if i < len(search_strategies) - 1:
                    time.sleep(1)  # Longer pause on error
                continue

        # If all strategies failed, return empty list
        logger.warning(f"⚠️ All search strategies failed for query: '{query}'")
        return []
    
    async def search_multiple_queries(
        self, 
        queries: List[str],
        search_type: str = "text"
    ) -> List[SearchResponse]:
        """
        Search multiple queries concurrently
        
        Args:
            queries: List of search queries
            search_type: Type of search
            
        Returns:
            List of SearchResponse objects
        """
        logger.info(f"🔍 Searching {len(queries)} queries concurrently")
        
        tasks = [
            self.search(query, search_type) 
            for query in queries
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        search_responses = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Search failed for query '{queries[i]}': {result}")
                search_responses.append(SearchResponse(
                    query=queries[i],
                    results=[],
                    total_results=0,
                    search_time=0.0,
                    success=False,
                    error=str(result)
                ))
            else:
                search_responses.append(result)
        
        return search_responses
    
    async def get_urls_from_query(self, query: str) -> List[str]:
        """
        Get just the URLs from a search query (convenience method)

        Args:
            query: Search query

        Returns:
            List of URLs
        """
        response = await self.search(query)
        if response.success:
            return [result.url for result in response.results]
        else:
            logger.warning(f"⚠️ Search failed, returning empty URL list")
            return []

    async def cleanup(self):
        """Cleanup search engine resources"""
        try:
            # Force cleanup of any remaining connections
            import gc
            import asyncio

            # Wait a bit for any pending operations
            await asyncio.sleep(0.1)

            # Force garbage collection multiple times
            for _ in range(3):
                gc.collect()
                await asyncio.sleep(0.05)

            logger.debug("✅ Search Engine Service cleanup completed")
        except Exception as e:
            logger.warning(f"⚠️ Search Engine Service cleanup warning: {e}")

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
