FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir \
    fastapi==0.104.1 \
    uvicorn==0.24.0 \
    pydantic==2.5.0 \
    aiohttp==3.9.1 \
    beautifulsoup4==4.12.2 \
    requests==2.31.0 \
    tls-client==1.0.1 \
    fake-useragent==1.4.0 \
    lxml==4.9.3

# Copy the entire mcp-integration directory
COPY mcp-integration/ ./mcp-integration/

# Copy the HTTP server
COPY jina_crawler_http_server.py .

# Set Python path to include jina_crawler modules
ENV PYTHONPATH="/app/mcp-integration/servers/jina_crawler:$PYTHONPATH"

# Set Gemini API key
ENV GEMINI_API_KEY="AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Start command
CMD ["python", "jina_crawler_http_server.py"]
