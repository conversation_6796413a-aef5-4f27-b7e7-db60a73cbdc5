#!/usr/bin/env python3
"""
Jina Crawler MCP OpenAPI Proxy Server
Compatible with Open WebUI's tool system
Based on successful MCPO pattern
"""

import asyncio
import sys
import os
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from datetime import datetime
from typing import List, Optional, Dict, Any

# Add mcp-integration path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mcp-integration/servers/jina_crawler'))

try:
    from jini_crawler import JiniCrawler
    from paywall_bypass_crawler import PaywallBypassCrawler
    from ai_search.ai_search_engine import AISearchEngine
except ImportError as e:
    print(f"Warning: Could not import jina_crawler modules: {e}")
    print("Will use mock implementations")
    JiniCrawler = None
    PaywallBypassCrawler = None
    AISearchEngine = None

app = FastAPI(
    title="MCP OpenAPI Proxy",
    description="""Automatically generated API from MCP Tool Schemas

- **available tools**：
    - [jina_crawler](/jina_crawler/docs)""",
    version="1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request models
class CrawlRequest(BaseModel):
    url: str
    method: Optional[str] = "tls_bypass"
    process_content: Optional[bool] = True
    max_content_length: Optional[int] = 10000

class SearchRequest(BaseModel):
    query: str
    max_sources: Optional[int] = 10
    enable_query_refinement: Optional[bool] = True

class BatchCrawlRequest(BaseModel):
    urls: List[str]
    method: Optional[str] = "tls_bypass"
    max_content_length: Optional[int] = 10000

class PaywallBypassRequest(BaseModel):
    url: str
    max_content_length: Optional[int] = 50000

# Initialize components
crawler = None
paywall_crawler = None
ai_search_engine = None

if JiniCrawler:
    try:
        crawler = JiniCrawler()
        print("✅ JiniCrawler initialized")
    except Exception as e:
        print(f"❌ Failed to initialize JiniCrawler: {e}")

if PaywallBypassCrawler:
    try:
        paywall_crawler = PaywallBypassCrawler()
        print("✅ PaywallBypassCrawler initialized")
    except Exception as e:
        print(f"❌ Failed to initialize PaywallBypassCrawler: {e}")

if AISearchEngine:
    try:
        ai_search_engine = AISearchEngine()
        print("✅ AISearchEngine initialized")
    except Exception as e:
        print(f"❌ Failed to initialize AISearchEngine: {e}")

@app.get("/")
async def root():
    return {
        "openapi": "3.1.0",
        "info": {
            "title": "MCP OpenAPI Proxy",
            "description": "Automatically generated API from MCP Tool Schemas\n\n- **available tools**：\n    - [jina_crawler](/jina_crawler/docs)",
            "version": "1.0"
        },
        "paths": {}
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "tools": ["jina_crawler"],
        "components": {
            "crawler": crawler is not None,
            "paywall_crawler": paywall_crawler is not None,
            "ai_search_engine": ai_search_engine is not None
        },
        "timestamp": datetime.now().isoformat()
    }

# Jina Crawler tool endpoints following MCP pattern
@app.get("/jina_crawler/docs")
async def jina_crawler_docs():
    """Swagger UI for jina_crawler tool"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
    <link type="text/css" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css">
    <link rel="shortcut icon" href="https://fastapi.tiangolo.com/img/favicon.png">
    <title>jina_crawler - Swagger UI</title>
    </head>
    <body>
    <div id="swagger-ui">
    </div>
    <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js"></script>
    <script>
    const ui = SwaggerUIBundle({
        url: '/jina_crawler/openapi.json',
        "dom_id": "#swagger-ui",
        "layout": "BaseLayout",
        "deepLinking": true,
        "showExtensions": true,
        "showCommonExtensions": true
    })
    </script>
    </body>
    </html>
    """

@app.get("/jina_crawler/openapi.json")
async def jina_crawler_openapi():
    return {
        "openapi": "3.1.0",
        "info": {
            "title": "jina_crawler",
            "description": "Smart web crawler with AI-powered content processing. Use this tool when users ask to crawl websites, extract content, search the web, or analyze web pages.",
            "version": "1.0.0"
        },
        "paths": {
            "/crawl_url": {
                "post": {
                    "summary": "Crawl and extract content from a website",
                    "description": "Use this tool when users ask to: crawl a website, get content from a URL, extract text from a webpage, read a website, or analyze web content.",
                    "operationId": "crawl_url",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "url": {
                                            "type": "string",
                                            "description": "The website URL to crawl",
                                            "example": "https://dantri.com.vn"
                                        },
                                        "max_content_length": {
                                            "type": "integer",
                                            "description": "Maximum content length",
                                            "default": 10000
                                        }
                                    },
                                    "required": ["url"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successfully crawled website",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "content": {"type": "string"},
                                            "title": {"type": "string"},
                                            "url": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/search_web": {
                "post": {
                    "summary": "Search the web with AI",
                    "description": "Use this tool when users ask to search the internet, find information online, or research a topic.",
                    "operationId": "search_web",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "query": {
                                            "type": "string",
                                            "description": "Search query",
                                            "example": "latest AI news"
                                        },
                                        "max_sources": {
                                            "type": "integer",
                                            "description": "Maximum number of sources",
                                            "default": 10
                                        }
                                    },
                                    "required": ["query"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Search results",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "results": {"type": "array"},
                                            "query": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/crawl_batch": {
                "post": {
                    "summary": "Crawl multiple websites",
                    "description": "Use this tool when users ask to crawl multiple URLs or compare content from several websites.",
                    "operationId": "crawl_batch",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "urls": {
                                            "type": "array",
                                            "items": {"type": "string"},
                                            "description": "List of URLs to crawl"
                                        },
                                        "max_content_length": {
                                            "type": "integer",
                                            "description": "Maximum content length per URL",
                                            "default": 10000
                                        }
                                    },
                                    "required": ["urls"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Batch crawl results",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "results": {"type": "array"},
                                            "total_urls": {"type": "integer"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/bypass_paywall": {
                "post": {
                    "summary": "Bypass paywall and extract premium content",
                    "description": "Use this tool when users ask to read content from paywall-protected websites, premium articles, or subscription-based content.",
                    "operationId": "bypass_paywall",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "url": {
                                            "type": "string",
                                            "description": "Paywall-protected URL to extract content from",
                                            "example": "https://premium-site.com/article"
                                        },
                                        "max_content_length": {
                                            "type": "integer",
                                            "description": "Maximum content length",
                                            "default": 50000
                                        }
                                    },
                                    "required": ["url"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successfully bypassed paywall and extracted content",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "content": {"type": "string"},
                                            "title": {"type": "string"},
                                            "url": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/ai_search": {
                "post": {
                    "summary": "AI-powered web search with intelligent results",
                    "description": "Use this tool when users ask to search for information online, research topics, find latest news, or get comprehensive search results with AI processing.",
                    "operationId": "ai_search",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "query": {
                                            "type": "string",
                                            "description": "Search query",
                                            "example": "latest AI developments 2024"
                                        },
                                        "max_sources": {
                                            "type": "integer",
                                            "description": "Maximum number of sources to search",
                                            "default": 10
                                        },
                                        "enable_query_refinement": {
                                            "type": "boolean",
                                            "description": "Enable AI query refinement",
                                            "default": True
                                        }
                                    },
                                    "required": ["query"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "AI search results with processed content",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "results": {"type": "array"},
                                            "query": {"type": "string"},
                                            "refined_query": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

@app.post("/jina_crawler/crawl_url")
async def crawl_url(request: CrawlRequest):
    """Crawl a single URL - MCP compatible endpoint"""
    if not crawler:
        return {"success": False, "error": "Crawler not available", "content": "Mock crawl result for: " + request.url}

    try:
        start_time = datetime.now()

        # Initialize crawler if needed
        if not crawler._initialized:
            await crawler.initialize()

        # Use the actual crawler with correct method name
        result = await crawler.crawl_and_process(
            url=request.url,
            max_content_length=request.max_content_length
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": result.success,
            "content": result.processed_content or result.raw_content or "",
            "title": result.title or "",
            "url": result.url,
            "processing_time": processing_time,
            "method": request.method,
            "error": result.error if not result.success else None
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "url": request.url,
            "content": f"Error crawling {request.url}: {str(e)}"
        }

@app.post("/jina_crawler/search_web")
async def search_web(request: SearchRequest):
    """Search the web - MCP compatible endpoint"""
    if not ai_search_engine:
        return {
            "success": False,
            "error": "AI Search Engine not available",
            "results": [{"title": f"Mock search result for: {request.query}", "url": "https://example.com", "snippet": "Mock content"}]
        }

    try:
        start_time = datetime.now()

        results = await ai_search_engine.search(
            query=request.query,
            max_sources=request.max_sources,
            enable_query_refinement=request.enable_query_refinement
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": True,
            "query": request.query,
            "results": results,
            "processing_time": processing_time
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "query": request.query,
            "results": []
        }

@app.post("/jina_crawler/crawl_batch")
async def crawl_batch(request: BatchCrawlRequest):
    """Crawl multiple URLs - MCP compatible endpoint"""
    if not crawler:
        return {
            "success": False,
            "error": "Crawler not available",
            "results": [{"url": url, "content": f"Mock result for {url}"} for url in request.urls]
        }

    try:
        start_time = datetime.now()

        # Initialize crawler if needed
        if not crawler._initialized:
            await crawler.initialize()

        # Use batch processing for efficiency
        batch_raw_data = await crawler.crawl_batch_raw(
            urls=request.urls,
            max_content_length=request.max_content_length
        )

        # Process with Gemini in batch
        batch_results = await crawler.process_batch_with_gemini(batch_raw_data)

        # Format results
        results = []
        for result in batch_results:
            results.append({
                "url": result.url,
                "success": result.success,
                "content": result.processed_content or result.raw_content or "",
                "title": result.title or "",
                "error": result.error if not result.success else None
            })

        processing_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": True,
            "results": results,
            "total_urls": len(request.urls),
            "processing_time": processing_time
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "results": []
        }

@app.post("/jina_crawler/bypass_paywall")
async def bypass_paywall(request: PaywallBypassRequest):
    """Bypass paywall and extract content - MCP compatible endpoint"""
    if not paywall_crawler:
        return {
            "success": False,
            "error": "Paywall Crawler not available",
            "content": f"Mock paywall bypass result for: {request.url}"
        }

    try:
        start_time = datetime.now()

        result = await paywall_crawler.bypass_and_extract(
            url=request.url,
            max_content_length=request.max_content_length
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": result.success,
            "content": result.processed_content or result.raw_content or "",
            "title": result.title or "",
            "url": result.url,
            "processing_time": processing_time,
            "error": result.error if not result.success else None
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "url": request.url,
            "content": f"Error bypassing paywall for {request.url}: {str(e)}"
        }

@app.post("/jina_crawler/ai_search")
async def ai_search(request: SearchRequest):
    """AI-powered search with advanced processing - MCP compatible endpoint"""
    if not ai_search_engine:
        return {
            "success": False,
            "error": "AI Search Engine not available",
            "results": [{"title": f"Mock AI search result for: {request.query}", "url": "https://example.com", "snippet": "Mock AI processed content"}]
        }

    try:
        start_time = datetime.now()

        # Use advanced AI search with query refinement
        results = await ai_search_engine.advanced_search(
            query=request.query,
            max_sources=request.max_sources,
            enable_query_refinement=request.enable_query_refinement,
            use_ai_processing=True
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": True,
            "query": request.query,
            "refined_query": getattr(results, 'refined_query', request.query),
            "results": results.results if hasattr(results, 'results') else results,
            "processing_time": processing_time,
            "ai_processed": True
        }

    except Exception as e:
        # Fallback to regular search if advanced search fails
        try:
            results = await ai_search_engine.search(
                query=request.query,
                max_sources=request.max_sources,
                enable_query_refinement=request.enable_query_refinement
            )

            return {
                "success": True,
                "query": request.query,
                "results": results,
                "processing_time": (datetime.now() - start_time).total_seconds(),
                "ai_processed": False,
                "fallback": True
            }
        except Exception as fallback_error:
            return {
                "success": False,
                "error": f"AI Search failed: {str(e)}, Fallback failed: {str(fallback_error)}",
                "query": request.query,
                "results": []
            }

if __name__ == "__main__":
    print("🚀 Starting Jina Crawler MCP Proxy Server on port 8002")
    print("📋 Compatible with Open WebUI tool system")
    print("🔗 Following MCPO pattern for tool discovery")
    uvicorn.run(app, host="0.0.0.0", port=8002)
